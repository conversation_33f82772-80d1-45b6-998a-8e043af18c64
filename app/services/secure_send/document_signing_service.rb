# frozen_string_literal: true

module SecureSend
  class DocumentSigningService
    def initialize(patient, document, user)
      @patient = patient
      @document = document
      @user = user
    end

    def create_signature_request(template_text: nil, due_by: nil, charting_appointment_id: nil,
                                 sent_at: nil, letter_fields: nil)
      attrs = {
        patient: @patient,
        signable_document: @document,
        created_by: @user,
        template_text: template_text,
        due_by: due_by,
        access_token: SecureRandom.urlsafe_base64(32),
        charting_appointment_id: charting_appointment_id,
        sent_by: @user,
        sent_at: sent_at,
        status: 'pending'
      }

      attrs[:status] = 'ready_to_send' if sent_at.present?

      signature_request = SignatureRequest.create!(attrs)

      if letter_fields.present?
        letter = create_patient_letter(@document, letter_fields, template_text)

        Stannp::LetterSender.new.perform(letter)

        signature_request.update!(status: 'completed')

        {
          signature_request: signature_request
        }
      else
        signing_url = Rails.application.routes.url_helpers.patients_signature_request_url(
          token: signature_request.access_token,
          host: Rails.application.config.action_mailer.default_url_options[:host]
        )

        SigningNotificationService.new(signature_request).send_signing_request if sent_at.blank?

        {
          signature_request: signature_request,
          signing_url: signing_url
        }
      end
    end

    def process_signature(signature_request, signature_data, request_data)
      # Update signature request with device info
      signature_request.update(
        ip_address: request_data[:ip],
        user_agent: request_data[:user_agent],
        device_type: request_data[:device_type],
        os_name: request_data[:os],
        os_version: request_data[:os_version],
        browser_name: request_data[:browser],
        browser_version: request_data[:browser_version]
      )

      # Update signature-specific data
      case signature_data[:type]
      when 'draw'
        signature_request.update(
          signature_type: 'draw',
          signed_name: signature_request.patient.full_name
        )
        attach_signature_to_request(signature_request, signature_data[:image]) if signature_data[:image].present?
      when 'typed'
        signature_request.update(
          signature_type: 'typed',
          signed_name: signature_data[:name]
        )
      when 'initials'
        # For initials, we don't update the signature_type
        # Just store the initials if needed
      when 'date'
        signature_request.update(
          date_fields: [*signature_request.date_fields, signature_data[:name]]
        )
      end

      signature_request
    end

    def process_template_content(html_content, patient)
      # Replace merge tags with patient data
      replaced_processed_content = replace_merge_tags(html_content, patient)

      # Extract signature fields - this now includes position and occurrence info
      signature_fields = extract_signature_fields(replaced_processed_content)

      # Sort signature fields by position in reverse order (to avoid position shifts)
      sorted_fields = signature_fields.sort_by { |field| -field[:position] }

      # Make a copy of the HTML content
      processed_content = replaced_processed_content.dup

      # Replace each signature field individually
      sorted_fields.each do |field|
        field_type = field[:type]
        merge_tag = field[:merge_tag]
        occurrence = field[:occurrence]

        # Create a unique ID for this field occurrence
        unique_id = "signature-#{field[:id]}-#{occurrence}"

        replacement = "<span class='signature-field-inline' " \
                     "data-field-id='#{field[:id]}' " \
                     "data-field-type='#{field_type}' " \
                     "data-occurrence='#{occurrence}' " \
                     "id='#{unique_id}' " \
                     "style='padding: 4px 8px; color: #007bff; border: 2px dotted black; font-style: italic;'>" \
                     "#{field[:label].gsub(' Inline', '')}</span>"

        # Replace JUST THIS ONE occurrence
        processed_content = processed_content.sub(merge_tag, replacement)
      end

      # Return both the processed content and signature fields
      {
        html_content: processed_content,
        signature_fields: signature_fields
      }
    end

    # Helper method to determine the field type
    def determine_field_type(field_name)
      case field_name
      when 'patient_signature'
        'signature'
      when 'patient_initials'
        'initials'
      when 'date'
        'date'
      else
        'signature' # Default type
      end
    end

    # Helper method to get a friendly label for the field
    def get_field_label(field_name)
      case field_name
      when 'patient_signature'
        'Signature'
      when 'patient_initials'
        'Initials'
      when 'date'
        'Date'
      else
        field_name.titleize
      end
    end

    def generate_template_pdf(signature_request)
      # Add this line to check if this method is called
      document = signature_request.signable_document

      if document.document_template_id.present?
        template = document.document_template
        html_content = signature_request.template_text || template.text

        # Process the template content
        result = process_template_content(html_content, signature_request.patient)

        # Generate PDF with signature fields
        pdf_service = SecureSend::PdfProcessingService.new(signature_request)
        pdf_content = pdf_service.generate_template_pdf_with_signature_fields(
          result[:html_content],
          result[:signature_fields]
        )

        # Create a temporary file for the PDF
        temp_file = Tempfile.new(['template', '.pdf'])
        temp_file.binmode
        temp_file.write(pdf_content)
        temp_file.close

        {
          pdf_path: temp_file.path,
          signature_fields: result[:signature_fields]
        }
      else
        { error: 'No document template found' }
      end
    end

    def complete_signing(signature_request)
      signature_request.update(
        status: 'completed',
        completed_at: Time.current
      )

      # Generate the final signed PDF
      if signature_request.signable_document.document_template_id.present?
        # For template-based documents, first get the processed HTML content
        template = signature_request.signable_document.document_template
        result = process_template_content(signature_request.template_text || template.text, signature_request.patient)

        # Replace signature fields with green boxes containing signature data
        processed_html = replace_signature_fields_with_data(
          result[:html_content],
          result[:signature_fields],
          signature_request
        )

        # Convert HTML to PDF and add signatures
        pdf_service = SecureSend::PdfProcessingService.new(signature_request)

        # Generate the PDF with signature fields and add audit trail
        pdf_content = pdf_service.generate_signed_pdf_with_audit(
          html_content: processed_html,
          signature_fields: result[:signature_fields]
        )

        return { error: 'Failed to generate signed PDF' } if pdf_content.blank?

        # Attach the signed PDF to the document
        temp_file = Tempfile.new(['signed', '.pdf'])
        temp_file.binmode
        temp_file.write(pdf_content)
        temp_file.rewind

        signature_request.signable_document.signed_pdf.attach(
          io: temp_file,
          filename: "#{signature_request.signable_document.title.parameterize}-signed.pdf",
          content_type: 'application/pdf'
        )

        temp_file.rewind

        asset = signature_request.patient.patient_assets.new(label: 'Signed Document')
        asset.file.attach(
          io: temp_file,
          filename: "#{signature_request.signable_document.title.parameterize}-signed.pdf",
          content_type: 'application/pdf'
        )
        asset.save

        temp_file.close
        temp_file.unlink

      elsif signature_request.signable_document.document_html.present?
        signature_fields = [
          {
            id: 'patient_signature',
            type: 'signature',
            label: 'Patient Signature'
          }
        ]

        processed_html = replace_signature_fields_with_data(
          signature_request.signable_document.document_html,
          signature_fields,
          signature_request
        )

        pdf_service = SecureSend::PdfProcessingService.new(signature_request)
        pdf_content = pdf_service.generate_signed_pdf_with_audit(
          html_content: processed_html,
          signature_fields: signature_fields
        )

        return { error: 'Failed to generate signed PDF' } if pdf_content.blank?

        temp_file = Tempfile.new(['signed', '.pdf'])
        temp_file.binmode
        temp_file.write(pdf_content)
        temp_file.rewind

        signature_request.signable_document.signed_pdf.attach(
          io: temp_file,
          filename: "#{signature_request.signable_document.title.parameterize}-signed.pdf",
          content_type: 'application/pdf'
        )

        temp_file.rewind

        asset = signature_request.patient.patient_assets.new(label: 'Signed Document')
        asset.file.attach(
          io: temp_file,
          filename: "#{signature_request.signable_document.title.parameterize}-signed.pdf",
          content_type: 'application/pdf'
        )
        asset.save

        temp_file.close
        temp_file.unlink

      elsif signature_request.signable_document.treatment_plan_option_id.present?
        treatment_plan_option = signature_request.signable_document.treatment_plan_option
        plan = treatment_plan_option.treatment_plan

        if treatment_plan_option.update(status: 'Accepted', signed_at: DateTime.now.in_time_zone('Europe/London'))
          plan.treatment_plan_options.where.not(id: treatment_plan_option.id).update_all(status: 'Declined')
        end

        # For documents with an original file, add signature page to the original PDF
        pdf_service = SecureSend::PdfProcessingService.new(signature_request)
        pdf_content = pdf_service.generate_signed_pdf_with_audit(
          treatment_plan_option_pdf: signature_request.signable_document.original_file
        )

        return { error: 'Failed to generate signed PDF' } if pdf_content.blank?

        temp_file = Tempfile.new(['signed', '.pdf'])
        temp_file.binmode
        temp_file.write(pdf_content)
        temp_file.close

        signature_request.signable_document.signed_pdf.attach(
          io: File.open(temp_file.path),
          filename: "#{signature_request.signable_document.title.parameterize}-signed.pdf",
          content_type: 'application/pdf'
        )

        temp_file.unlink if File.exist?(temp_file.path)
      end

      signature_request.patient.assigned_staff.uniq.each do |user|
        patient = signature_request.patient.full_name
        doc_name = signature_request.signable_document.title
        completed_at = signature_request.completed_at
        day = completed_at.day
        day_with_ordinal = case day % 100
                           when 11, 12, 13 then "#{day}th"
                           else
                             case day % 10
                             when 1 then "#{day}st"
                             when 2 then "#{day}nd"
                             when 3 then "#{day}rd"
                             else "#{day}th"
                             end
                           end
        date = completed_at.strftime('%a') + " #{day_with_ordinal} " + completed_at.strftime('%B at %H:%M')
        text = "#{patient} signed #{doc_name} on #{date}. The signed document has been saved to the patient’s assets."
        Notification.create(
          recipient: user,
          title: "#{patient} Signed Document: #{doc_name}",
          description: text,
          icon: 'signature',
          actions: [
            { text: 'View Document', primary: true, action: 'redirect', href: "/admin/signature_requests/#{signature_request.id}" },
            { text: 'Mark Read', action: 'mark_as_read' }
          ],
          data: { type: 'documents_and_consents',
                  color: '#8B5CF6',
                  sender: signature_request.patient.full_name,
                  avatar_url: signature_request.patient.image&.url }
        )
      end

      SigningNotificationService.new(signature_request).send_signing_confirmation

      { success: true }
    end

    private

    def create_patient_letter(signable_document, letter_fields, template_text)
      Letter.create!(
        practice_id: Current.practice_id,
        patient_id: @patient.id,
        user_id: @user.id,
        signable_document_id: signable_document.id,
        body: replace_letter_merge_tags(template_text),
        from_address_line_1: letter_fields[:from_address_line_1],
        from_address_line_2: letter_fields[:from_address_line_2],
        from_address_line_3: letter_fields[:from_address_line_3],
        from_county: letter_fields[:from_county],
        from_email_address: letter_fields[:from_email_address],
        from_name: letter_fields[:from_name],
        from_phone_number: letter_fields[:from_phone_number],
        from_postcode: letter_fields[:from_postcode],
        from_region: letter_fields[:from_region],
        from_town: letter_fields[:from_town],
        from_website: letter_fields[:from_website],
        to_address_line_1: letter_fields[:to_address_line_1],
        to_address_line_2: letter_fields[:to_address_line_2],
        to_address_line_3: letter_fields[:to_address_line_3],
        to_county: letter_fields[:to_county],
        to_name: letter_fields[:to_name],
        to_postcode: letter_fields[:to_postcode],
        to_region: letter_fields[:to_region],
        to_town: letter_fields[:to_town]
      )
    end

    def replace_letter_merge_tags(template_text)
      template_text
        .gsub('{{user.first_name}}', @patient.first_name.to_s)
        .gsub('{{user.last_name}}',  @patient.last_name.to_s)
        .gsub('{{user.email}}',      @patient.email.to_s)
        .gsub(/\{\{\s*signature:[^}]+\}\}/i, '')
        .gsub(/\{\}/, '')
    end

    def attach_signature_to_request(signature_request, signature_image_data)
      # Convert base64 data to image and attach
      decoded_data = Base64.decode64(signature_image_data.split(',')[1])
      signature_request.signature_images.attach(
        io: StringIO.new(decoded_data),
        filename: "signature_#{signature_request.id}.png",
        content_type: 'image/png'
      )
    end

    def replace_merge_tags(html_content, patient)
      return html_content if patient.blank?

      # Replace patient-related merge tags
      content = html_content.dup
      # Basic patient information
      content = content.gsub(/\{\{patient\.name\}\}/) { |_match| "<span data-merge-tag=\"patient.name\">#{patient.full_name}</span>" }
      content = content.gsub(/\{\{patient\.first_name\}\}/) do |_match|
        "<span data-merge-tag=\"patient.first_name\">#{patient.first_name}</span>"
      end
      content = content.gsub(/\{\{user\.first_name\}\}/) do |_match|
        "<span data-merge-tag=\"user.first_name\">#{patient.first_name}</span>"
      end
      content = content.gsub(/\{\{patient\.last_name\}\}/) do |_match|
        "<span data-merge-tag=\"patient.last_name\">#{patient.last_name}</span>"
      end
      content = content.gsub(/\{\{user\.last_name\}\}/) do |_match|
        "<span data-merge-tag=\"user.last_name\">#{patient.last_name}</span>"
      end
      content = content.gsub(/\{\{patient\.dob\}\}/) do |_match|
        "<span data-merge-tag=\"patient.dob\">#{patient.date_of_birth&.strftime('%d/%m/%Y')}</span>"
      end
      content = content.gsub(/\{\{patient\.email\}\}/) { |_match| "<span data-merge-tag=\"patient.email\">#{patient.email}</span>" }
      content = content.gsub(/\{\{user\.email\}\}/) { |_match| "<span data-merge-tag=\"user.email\">#{patient.email}</span>" }
      content = content.gsub(/\{\{patient\.phone\}\}/) { |_match| "<span data-merge-tag=\"patient.phone\">#{patient.phone_number}</span>" }

      # Address information if available
      if patient.respond_to?(:address)
        content = content.gsub(/\{\{patient\.address\}\}/) { |_match| "<span data-merge-tag=\"patient.address\">#{patient.address}</span>" }
      end

      # Date information
      content = content.gsub(/\{\{date\.today\}\}/) do |_match|
        "<span data-merge-tag=\"date.today\">#{Time.zone.today.strftime('%d/%m/%Y')}</span>"
      end
      content.gsub(/\{\{date\.now\}\}/) do |_match|
        "<span data-merge-tag=\"date.now\">#{Time.zone.now.strftime('%d/%m/%Y %H:%M')}</span>"
      end

      # Add more replacements as needed
    end

    def extract_signature_fields(html_content)
      signature_fields = []

      # Triple-brace pattern
      signature_pattern = /\{\{\{signature:([^}]+)\}\}\}/

      # Track occurrences
      occurrence_counters = {}

      # Find all occurrences of signature fields with their positions
      html_content.scan(signature_pattern) do |match|
        field_id = match[0]
        match_text = Regexp.last_match[0]

        # Keep track of occurrences of each field type
        occurrence_counters[field_id] ||= 0
        occurrence_counters[field_id] += 1

        # Determine field type
        field_type = if field_id.include?('initial')
                       'initials'
                     elsif field_id.include?('date')
                       'date'
                     else
                       'signature'
                     end

        # Create a field object with position information
        field = {
          id: field_id,
          type: field_type,
          label: get_field_label(field_id),
          merge_tag: match_text,
          position: Regexp.last_match.offset(0)[0],
          occurrence: occurrence_counters[field_id]
        }

        signature_fields << field
      end

      signature_fields
    end

    def replace_signature_fields_with_data(html_content, signature_fields, signature_request)
      processed_html = html_content.dup

      # Replace each signature field with a green box containing the signature data
      signature_index = 0
      date_index = 0
      signature_fields.each do |field|
        field_type = field[:type]
        field_id = field[:id]
        merge_tag = field[:merge_tag]

        # Get signature data and update indices
        signature_data, signature_index, date_index = process_signature_field(
          field_type, signature_request, signature_index, date_index
        )

        # Create a green box with the signature data - styled to match the second image
        replacement = <<~HTML
          <div style="border: 1px solid #28a745; background-color: #e8f5e9; padding: 10px; margin: 10px 0; position: relative; min-height: 80px; border-radius: 4px;">
            <div style="text-align: center; font-weight: bold; margin-bottom: 10px; font-family: Arial, sans-serif;">#{field_type.capitalize}</div>
            #{signature_data}
          </div>
        HTML

        # Replace the merge tag with the green box
        if merge_tag.present?
          processed_html = processed_html.gsub(merge_tag, replacement)
        elsif field_id.present?
          # For custom HTML documents with signature placeholders
          placeholder = "{{{signature:#{field_id}}}}"
          processed_html = processed_html.gsub(placeholder, replacement)
        end
      end

      processed_html
    end

    def process_signature_field(field_type, signature_request, signature_index, date_index)
      # Get the appropriate signature data based on field type
      signature_data = ''
      case field_type
      when 'signature'
        if signature_request.signature_image.attached?
          # For drawn signatures, we'll use an embedded image
          begin
            # Get the image data as a base64 string to ensure it displays properly
            image_data = Base64.strict_encode64(signature_request.signature_images.blobs.order(:created_at)[signature_index].download)
            signature_data = "<img src='data:image/png;base64,#{image_data}' " \
                            "style='max-width: 100%; max-height: 80px; display: block; margin: 0 auto;'>"
            signature_index += 1
          rescue StandardError => e
            # Fallback to text if image can't be loaded
            Rails.logger.error("Error loading signature image: #{e.message}")
            signature_data = "<div style='text-align: center; font-style: italic; " \
                             "font-size: 16px; font-family: \"Times New Roman\", Times, serif;'>" \
                             "#{signature_request.signed_name}</div>"
          end
        else
          # For typed signatures
          signature_data = "<div style='text-align: center; font-style: italic; " \
                           "font-size: 16px; font-family: \"Times New Roman\", Times, serif;'>" \
                           "#{signature_request.signed_name}</div>"
        end
      when 'initials'
        # Generate initials from patient's name
        patient = signature_request.patient
        initials = ''
        if patient
          # Get first letter of first name and first letter of last name
          first_initial = patient.first_name.to_s[0]
          last_initial = patient.last_name.to_s[0]
          initials = "#{first_initial}#{last_initial}"
        end
        signature_data = "<div style='text-align: center; font-style: italic; " \
                         "font-size: 16px; font-family: \"Times New Roman\", Times, serif;'>" \
                         "#{initials}</div>"
      when 'date'
        # Use completed_at date or today's date
        date_str = signature_request.date_fields[date_index]
        signature_data = "<div style='text-align: center; font-size: 16px; font-family: Arial, sans-serif;'>#{date_str}</div>"
        date_index += 1
      end

      [signature_data, signature_index, date_index]
    end
  end
end
