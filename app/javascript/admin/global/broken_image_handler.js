document.addEventListener('DOMContentLoaded', function() {
  function handleBrokenImage(img) {
    if (img.id === 'imageModalImage') return;

    img.style.display = 'none';

    const placeholder = document.createElement('div');
    placeholder.className = 'broken-image-placeholder';
    placeholder.style.cssText = `
      width: ${img.width || '24'}px;
      height: ${img.height || '24'}px;
      background-color: #f3f4f6;
      border: 1px solid #e5e7eb;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #9ca3af;
      font-size: 12px;
      text-align: center;
    `;

    if (img.parentNode && (img.width > 0 || img.height > 0)) {
      placeholder.innerHTML = '📷';
      img.parentNode.insertBefore(placeholder, img);
    }
  }

  function setupImageErrorHandlers() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (img.dataset.errorHandled) return;

      img.addEventListener('error', function() {
        handleBrokenImage(this);
      });

      img.dataset.errorHandled = 'true';

      if (img.complete && img.naturalWidth === 0) {
        handleBrokenImage(img);
      }
    });
  }

  function setupMutationObserver() {
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        mutation.addedNodes.forEach(function(node) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node is an image
            if (node.tagName === 'IMG') {
              if (!node.dataset.errorHandled) {
                node.addEventListener('error', function() {
                  handleBrokenImage(this);
                });
                node.dataset.errorHandled = 'true';

                if (node.complete && node.naturalWidth === 0) {
                  handleBrokenImage(node);
                }
              }
            }

            const childImages = node.querySelectorAll && node.querySelectorAll('img');
            if (childImages) {
              childImages.forEach(img => {
                if (!img.dataset.errorHandled) {
                  img.addEventListener('error', function() {
                    handleBrokenImage(this);
                  });
                  img.dataset.errorHandled = 'true';

                  if (img.complete && img.naturalWidth === 0) {
                    handleBrokenImage(img);
                  }
                }
              });
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  setupImageErrorHandlers();
  setupMutationObserver();
});

window.BrokenImageHandler = {
  handleBrokenImage: function(img) {
    img.style.display = 'none';
  }
};
