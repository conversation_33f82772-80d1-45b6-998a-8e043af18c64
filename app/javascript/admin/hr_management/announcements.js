// check if quill is already defined
let quill;

function searchUserInAnnouncement() {
  // User search functionality
  const searchBookInput = $('#userAnnouncementSearch');
  const userRows = document.querySelectorAll('[data-announcement-id]');

  console.log('Search input found:', !!searchBookInput);
  console.log('Number of user rows found:', userRows.length);

  if (searchBookInput && userRows.length > 0) {
    searchBookInput.off('keyup')
    searchBookInput.on('keyup', function () {
      const searchTerm = this.value.toLowerCase();
      console.log('Search term:', searchTerm);

      // Loop through each user row and check if the search term is in the username
      userRows.forEach(row => {
        const userName = $(row).data('author').toLowerCase();
        const announcementTitle = $(row).find('h2').text().toLowerCase();
        const announcementBody = $(row).find('p').text().toLowerCase();

        console.log('User name:', userName);
        console.log('Announcement title:', announcementTitle);
        console.log('Announcement body:', announcementBody);

        if (userName.includes(searchTerm) || announcementTitle.includes(searchTerm) || announcementBody.includes(searchTerm)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    });
  } else {
    console.log('Search functionality could not be initialized');
  }
}

function initQuill() {
  if (!quill) {
    quill = new Quill('#_announcement_text', {
      theme: 'snow',
      modules: {
        toolbar: [
          ['bold', 'italic', 'underline', 'strike'],
          [{'list': 'ordered'}, {'list': 'bullet'}],
          [{'header': [1, 2, 3, 4, 5, 6, false]}],
          ['link', 'image'],
          ['clean']
        ]
      }
    });
    quill.on('text-change', function() {
      // Sync the Quill editor content with the form's textarea (hidden or visible)
      $('#_announcement_text_hidden').val(quill.root.innerHTML);
    });
  }
}

function setupFormSubmission() {
  var form = document.querySelector('#announcementForm');
  form.onsubmit = function () {
    var text = document.querySelector('input[name="announcement[text]"]');
    text.value = quill.root.innerHTML;
  };
}

$(document).ready(function() {
  if(document.getElementById('announcements-container') === null){
    return;
  }

  // Check if the page contains the form with the specific ID
  if ($('#updateAnnouncementAudienceFormButton').length > 0) {
    // Attach the click event listener to the button
    $('#updateAnnouncementAudienceFormButton').on('click', function() {
      submitUpdateAnnouncementAudienceFormButton();
    });
  }

  // Custom modal functionality
  $('[data-bs-toggle="modal"]').on('click', function() {
    const target = $(this).data('bs-target');
    $(target).removeClass('hidden').addClass('show');
  });

  $('[data-bs-dismiss="modal"]').on('click', function() {
    $(this).closest('.modal').removeClass('show').addClass('hidden');
  });

  // Close modal when clicking outside
  $('.modal').on('click', function(e) {
    if (e.target === this) {
      $(this).removeClass('show').addClass('hidden');
    }
  });
});

function submitUpdateAnnouncementAudienceFormButton() {
  var audienceRoles = Array.from(document.querySelectorAll('input[name="edit_audience_roles[]"]:checked')).map(el => el.value);

  $.ajax({
    url: '/admin/hr_management/announcements/' + $('#announcement_id').val(),
    method: 'PATCH',
    dataType: 'json',
    headers: {
      'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
    },
    data: {
      announcement: {
        audience_teams: $('#audienceEditModal').find('select').val().join(','),
        audience_roles: audienceRoles.join(',')
      }

    },
    success: function (response) {
      $('#audienceEditModal').modal('hide');
      if (response.redirect_to) {
        window.location.href = response.redirect_to;
      }
    },
  })
}

function setupEditAnnouncement() {
  $('.edit-audience-announcement').off('click')
  $(document).on('click', '.edit-audience-announcement', function (e) {
    e.preventDefault();
    var announcementId = $(this).data('announcement-id');
    $('#announcementForm')[0].reset(); // Reset the form first
    // Fetch the announcement data
    $.ajax({
      url: '/admin/hr_management/announcements/' + announcementId + '/edit',
      method: 'GET',
      success: function (response) {
        $('#audienceEditModal').modal('show');
        // Populate the form with the fetched data
        $('#audienceEditModal').find('input[type=checkbox]').prop('checked', false)
        response.audience_roles.forEach(function (role) {
          $('#edit_role_' + role).prop('checked', true);
        });
        $('#audienceEditModal').find('select').select2('destroy')
        $('#audienceEditModal').find('select').val(response.audience_teams)
        $('#audienceEditModal').find('select').select2({
          placeholder: 'Choose users',
          allowClear: true,
        })

        $('#announcement_id').val(announcementId);
      },
      error: function (xhr, status, error) {
        console.error('Error fetching announcement data:', error);
        alert('Error fetching announcement data. Please try again.');
      }
    })
  });

  $('.edit-announcement').off('click')
  $(document).on('click', '.edit-announcement', function (e) {
    e.preventDefault();

    // No need for offcanvas-specific code with modal

    var announcementId = $(this).data('announcement-id');
    $('#announcementForm')[0].reset(); // Reset the form first
    $('#announcementModalLabel').text('Edit Announcement');
    // Fetch the announcement data
    $.ajax({
      url: '/admin/hr_management/announcements/' + announcementId + '/edit',
      method: 'GET',
      success: function (response) {
        console.log('Fetched announcement data:', response);
        // Populate the form with the fetched data

        // Add or update the method override input
        var methodField = $('#method-override');
        if (methodField.length === 0) {
          $('#announcementForm').prepend('<input type="hidden" name="_method" value="patch" id="method-override">');
          $('#announcementForm').prepend('<input type="hidden" name="direct_redirect" value="true" id="direct_redirect">');
        } else {
          methodField.val('patch');
        }
        $('input[name="announcement[title]"]').val(response.title);
        quill.root.innerHTML = response.text;
        $('select[name="announcement[topic]"]').val(response.topic);

        // Open the modal
        $("#createAnnouncementModal").removeClass('hidden').addClass('show');

        $('#announcementForm').attr('action', '/admin/hr_management/announcements/' + announcementId);
        $('#announcementForm').attr('method', 'post');

        // Populate the modal with the fetched data
        $('#audienceModal').find('input[type=checkbox]').prop('checked', false)
        response.audience_roles.forEach(function (role) {
          $('#role_' + role).prop('checked', true);
        });
        $('#audienceModal').find('select').select2('destroy')
        $('#audienceModal').find('select').val(response.audience_teams)
        $('#audienceModal').find('select').select2({
          placeholder: 'Choose users',
          allowClear: true,
        })

        $('#announcement_id').val(announcementId);
      },
      error: function (xhr, status, error) {
        console.error('Error fetching announcement data:', error);
        alert('Error fetching announcement data. Please try again.');
      }
    });
  });
}

// updateThumbnail function that needs to be able to handle multiple files
function updateThumbnail(file) {
  if (file.type.startsWith("image/")) {
    const thumbnailElement = new Image();
    thumbnailElement.classList.add("drop-zone__thumb");
    thumbnailElement.src = URL.createObjectURL(file);
    showSelectedImages.append(thumbnailElement);
  }
} // end of 'updateThumbnail' function

$(document).ready(function() {
  if(document.getElementById('announcements-container') === null){
    return;
  }

  // Check if the page contains the form with the specific ID
  if ($('#announcementForm').length > 0) {
    // Attach the click event listener to the button
    $('#publishAnnouncementFormButton').on('click', function() {
      publishAnnouncementFormButton();
    });
  }
});
function publishAnnouncementFormButton() {
  var form = document.querySelector('#announcementForm');
  var audienceRoles = Array.from(document.querySelectorAll('input[name="audience_roles[]"]:checked')).map(el => el.value);

  $('#announcement_audience_teams').val($('#audience_teams').val().join(','));
  $('#announcement_audience_roles').val(audienceRoles.join(','));
  form.submit();
}

function initFileUpload() {

  console.log('init file upload')

  // hidden on the form, but has drag & drop files assigned to it
  var fileUploader = document.getElementById('fileInput');
  var dropZone = document.getElementById('drop-zone');
  var showSelectedImages = document.getElementById('show-selected-images');

  $('#drop-zone').off('click')
  $('#drop-zone').on('click', function () {
    console.log('drop zone clicked')
    // Assigns the dropzone to the hidden input element so when you click 'select files' it brings up a file picker window
    fileUploader.click();
  });

  fileUploader.addEventListener('change', (e) => {
    Array.from(fileUploader.files).forEach((file) => {
      updateThumbnail(dropZone, file);
    });
  });

  dropZone.addEventListener('dragover', (e) => {
    e.preventDefault();
    console.log('dropZone.addEventListener(dragove) ')
    // ...
  });

  dropZone.addEventListener('dragend', (e) => {
    e.preventDefault();
    console.log('dropZone.addEventListener(dragend) ')
    // ...
  });

  dropZone.addEventListener('drop', (e) => {
    e.preventDefault();
    console.log('dropZone.addEventListener(DROP) ')
    // Assign dropped files to the hidden input element
    // But if you are not clearing preview then you need to concatenate files with previous
    if (e.dataTransfer.files.length) {
      fileUploader.files = e.dataTransfer.files;
    }

    Array.from(e.dataTransfer.files).forEach((file) => {
      updateThumbnail(dropZone, file);
    });
  });

  function removeFile(file) {
    var fileInput = document.getElementById('fileInput');
    var dataTransfer = new DataTransfer();

    Array.from(fileInput.files).forEach(f => {
      if (f !== file) {
        dataTransfer.items.add(f);
      }
    });

    fileInput.files = dataTransfer.files;
  }

  function updateThumbnail(dropZone, file) {
    if (file.type.startsWith('image/')) {
      // You could also use createObjectURL instead https://developer.mozilla.org/en-US/docs/Web/API/URL/createObjectURL
      var reader = new FileReader();

      reader.readAsDataURL(file);

      reader.onload = () => {
        var thumbnailElement = document.createElement('img');
        thumbnailElement.classList.add('drop-zone__thumb');
        thumbnailElement.src = reader.result;
        showSelectedImages.appendChild(thumbnailElement);
      };
    }
  }
}

document.addEventListener("DOMContentLoaded", function () {
  if(document.getElementById('announcements-container') === null){
    return;
  }

  quill = undefined;
  initFileUpload(); // Initialize FileUpload
  initQuill(); // Initialize Quill

  setupFormSubmission();
  setupEditAnnouncement();
  // select 2 with multiple select
  $('#audience_teams').select2({
    placeholder: 'Choose users',
    allowClear: true
  })
  $('#edit_audience_teams').select2({
    placeholder: 'Choose users',
    allowClear: true
  })

  searchUserInAnnouncement();
});


$(document).ready(function () {
  if(document.getElementById('announcements-container') === null){
    return;
  }

  $('#create-announcement-btn').on('click', function () {
    $('#announcementModalLabel').text('Create Announcement');
    let $announcementForm = $('#announcementForm')
    $announcementForm[0].reset(); // Reset the form first
    $('#method-override')[0]?.remove();
    $('#direct_redirect')[0]?.remove();
    quill.root.innerHTML = '';
    console.log($announcementForm[0])
    $announcementForm.attr('action', '/admin/hr_management/announcements/');
    $announcementForm.attr('method', 'post');
  });

  // Modern dropdown functionality
  const filterButton = document.getElementById('announcements-filter-button');
  const filterDropdown = document.getElementById('announcements-filter-dropdown');

  if (filterButton && filterDropdown) {
    filterButton.addEventListener('click', function(e) {
      e.stopPropagation();
      filterDropdown.classList.toggle('hidden');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      if (!filterButton.contains(event.target) && !filterDropdown.contains(event.target)) {
        filterDropdown.classList.add('hidden');
      }
    });

    // Handle filter option clicks
    const filterOptions = filterDropdown.querySelectorAll('.filter-option');
    filterOptions.forEach(option => {
      option.addEventListener('click', function(e) {
        e.preventDefault();

        // Remove active state and checkmarks from all options
        filterOptions.forEach(opt => {
          opt.classList.remove('active');
          const svg = opt.querySelector('svg');
          if (svg) {
            svg.style.display = 'none';
          }
        });

        // Add active state and checkmark to clicked option
        this.classList.add('active');
        let svg = this.querySelector('svg');
        if (!svg) {
          // Create checkmark if it doesn't exist
          svg = document.createElement('svg');
          svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
          svg.setAttribute('width', '16');
          svg.setAttribute('height', '16');
          svg.setAttribute('viewBox', '0 0 24 24');
          svg.setAttribute('fill', 'none');
          svg.setAttribute('stroke', 'currentColor');
          svg.setAttribute('stroke-width', '2');
          svg.setAttribute('stroke-linecap', 'round');
          svg.setAttribute('stroke-linejoin', 'round');
          svg.className = 'mr-2 text-green-600';
          svg.innerHTML = '<path d="M20 6 9 17l-5-5"></path>';
          this.insertBefore(svg, this.firstChild);
        }
        svg.style.display = 'block';

        // Update button text and sort
        const sortBy = this.dataset.sort;
        const filterLabel = document.getElementById('filter-label');
        const optionText = this.querySelector('span').textContent;
        filterLabel.textContent = `Filter: ${optionText}`;

        // Close dropdown
        filterDropdown.classList.add('hidden');

        // Apply sort
        sortAnnouncements(sortBy);
      });
    });
  }

  function sortAnnouncements(sortBy) {
    $.ajax({
      url: '/admin/hr_management/announcements',
      method: 'GET',
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      data: {sort: sortBy},
      success: function (response) {
        // Update the announcements container with the sorted announcements
        $('.grid.grid-cols-1.gap-6').html(response);
      },
      error: function (xhr, status, error) {
        console.error('Error sorting announcements:', error);
      }
    });
  }


});


$(document).ready(function () {
  if(document.getElementById('announcements-container') === null){
    return;
  }

  tippy('.reaction', {
    placement: 'top',
    theme: 'light-border',
    arrow: true,
  });

  // Reaction handling
  $(document).on('click', '.reaction-button', function (e) {
    e.stopPropagation();
    $(this).siblings('.reaction-popup').toggle();
  });

  $(document).click(function () {
    $('.reaction-popup').hide();
  });

  $(document).on('click', '.reaction', function () {
    var $reactions = $(this).closest('.reactions');
    var announcementId = $reactions.data('announcement-id');
    var emoji = $(this).text().trim().split(' ')[0];
    $.ajax({
      url: '/admin/hr_management/announcement_reactions/' + announcementId,
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      method: 'DELETE',
      data: {emoji: emoji},
      success: function (response) {
        updateReactionDisplay($reactions, response.reactions);
      }
    });
  });

  $(document).on('click', '.emoji', function () {
    var $reactions = $(this).closest('.reactions');
    var announcementId = $reactions.data('announcement-id');
    var emoji = $(this).data('emoji');
    $.ajax({
      url: '/admin/hr_management/announcement_reactions',
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      method: 'POST',

      data: {announcement_id: announcementId, emoji: emoji},
      success: function (response) {
        updateReactionDisplay($reactions, response.reactions);
      }
    });

    $(this).closest('.reaction-popup').hide();
  });

  function updateReactionDisplay($reactions, reactions) {
    var $display = $reactions.find('.reaction-display');
    if (Object.keys(reactions).length === 0) {
      $display.text('No reactions yet');
    } else {
      $display.empty();
      $.each(reactions, function (emoji, data) {
        const tippyContent = data['users'].map(user => user['name']).join('') + ' reacted with ' + emoji;
        $display.append(
          '<span class="reaction" data-tippy-content="' + tippyContent + '">' + emoji + ' <small>' + data['count'] + '</small></span>'
        );
      });

      tippy('.reaction', {
        placement: 'top',
        theme: 'light-border',
        arrow: true,
      });
    }
  }

  // NOTE: comments are disabled for now (20 January 2025)
  // Comment handling
  // $(document).on('click', '.comments-toggle', function (e) {
  //     e.stopPropagation();
  //     var $popup = $(this).parent().parent().parent().next().find(".comments-popup");
  //     $popup.toggle();

  //     if ($popup.is(':visible')) {
  //         var $announcement = $(this).closest('.announcement');
  //         loadComments($announcement);
  //     }
  // });

  // $(document).on('click', '.comments-popup', function (e) {
  //     e.stopPropagation();
  // });

  // $(document).on('click', '.load-more-link', function (e) {
  //     e.preventDefault();
  //     var $announcement = $(this).closest('.announcement');
  //     loadComments($announcement, true);
  // });

  // $(document).on('submit', '.comment-form', function (e) {
  //     e.preventDefault();
  //     var $form = $(this);
  //     var $announcement = $form.closest('.announcements');
  //     var announcementId = $form.data('announcement-id');
  //     var content = $form.find('.comment-input').val();

  //     if (!announcementId) {
  //         console.error('Announcement ID not found');
  //         return;
  //     }

  //     $.ajax({
  //         url: '/admin/hr_management/create_comment',
  //         method: 'POST',
  //         headers: {
  //             'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
  //         },
  //         data: {announcement_id: announcementId, comment: {content: content}},
  //         success: function (response) {
  //             if (response.success) {
  //                 var $commentsContainer = $announcement.find('.comments-container');
  //                 $commentsContainer.prepend(response.comment);
  //                 $form.find('.comment-input').val('');
  //                 updateCommentCount($announcement);
  //                 updateTimestamps();
  //                 console.log('New comment added:', response.comment); // Add this line for debugging
  //             } else {
  //                 alert('Error posting comment: ' + response.errors.join(', '));
  //             }
  //         },
  //         error: function (xhr, status, error) {
  //             console.error('Error posting comment:', error);
  //             alert('Error posting comment. Please try again.');
  //         }
  //     });
  // });

  // function loadComments($announcement, append = false) {
  //     var announcementId = $announcement.data('announcement-id');
  //     var $container = $announcement.find('.comments-container');
  //     var offset = $container.children().length;

  //     $.ajax({
  //         url: '/admin/hr_management/load_comments', // Note the underscore instead of hyphen
  //         method: 'GET',
  //         headers: {
  //             'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
  //         },
  //         data: {announcement_id: announcementId, offset: offset},
  //         success: function (response) {
  //             console.log('Loaded comments:', response.comments);
  //             if (append) {
  //                 $container.append(response.comments);
  //             } else {
  //                 $container.html(response.comments);
  //             }

  //             var $loadMore = $announcement.find('.load-more-comments');
  //             if (response.has_more) {
  //                 $loadMore.show();
  //             } else {
  //                 $loadMore.hide();
  //             }
  //             updateTimestamps();
  //             updateCommentCount($announcement);
  //         },
  //         error: function (xhr, status, error) {
  //             console.error('Error loading comments:', error);
  //         }
  //     });
  // }

  // function updateCommentCount($announcement) {
  //     var count = $announcement.find('.comments-container .comment').length;

  //     if (count === 0) {
  //         $announcement.find('.comment-count').text('No Comments');
  //     } else {
  //         $announcement.find('.comment-count').text(`View ${count} Comment${count > 1 ? 's' : ''}`);
  //     }
  // }

  function updateTimestamps() {
    $('.timestamp').each(function () {
      var timestamp = $(this).data('timestamp');
      var timeAgo = formatTimeAgo(new Date(timestamp));
      $(this).text(timeAgo);
    });
  }

  function formatTimeAgo(date) {
    var seconds = Math.floor((new Date() - date) / 1000);
    var interval = Math.floor(seconds / 31536000);

    if (interval > 1) {
      return interval + " years ago";
    }
    interval = Math.floor(seconds / 2592000);
    if (interval > 1) {
      return interval + " months ago";
    }
    interval = Math.floor(seconds / 86400);
    if (interval > 1) {
      return interval + " days ago";
    }
    interval = Math.floor(seconds / 3600);
    if (interval > 1) {
      return interval + " hours ago";
    }
    interval = Math.floor(seconds / 60);
    if (interval > 1) {
      return interval + " minutes ago";
    }
    return Math.floor(seconds) + " seconds ago";
  }

  // Initial load
  $('.reactions').each(function () {
    var $reactions = $(this);
    var announcementId = $reactions.data('announcement-id');

    $.ajax({
      url: '/admin/hr_management/announcement_reactions/' + announcementId,
      method: 'GET',
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      success: function (response) {
        updateReactionDisplay($reactions, response.reactions);
      }
    });
  });

  // $('.announcements').each(function () {
  //     loadComments($(this));
  // });

  // Update timestamps every minute
  setInterval(updateTimestamps, 60000);
});

