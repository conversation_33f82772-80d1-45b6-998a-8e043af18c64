#announcements-container {
  .cancelbtn {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    margin-right: 6px;
    padding: 8px;
    border-radius: 37px;
    border: 1px solid #2A4558;
    color: #2A4558;
    background: white;
    justify-content: center;
    transition: background-color 0.3s ease;
    cursor: pointer;
    height: 37px;
    font-size: 14px;
    width: 120px;
  }

  .card {
    background: white;
  }

  .title {
    font-size: 26px;
    font-weight: bold;
    color: #151515;
  }

  .header {
    padding: 10px 20px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
  }

  // Hide scrollbar for webkit browsers
  .hide-scrollbar {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  #userAnnouncementSearch:focus {
    box-shadow: none;
    border-color: #2A4558;
  }

  .select2-search__field{
    padding-left: 6px !important;
    font-family: Poppins, sans-serif;
  }

  .form-control, .form-select, .form-label, .form-check-label, .form-check-input, .form-check {
    font-size: 14px;
    font-weight: 400;
    font-family: 'Poppins';
  }

  .form-check {
    margin-left: 1rem;
  }

  .form-check .form-check-input {
    margin-left: -2rem;
  }

  .offcanvas-header {
    padding: 19px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    border-bottom: 1px solid #e5e5e5;
    margin: 0 4px 0 20px;
  }

  .offcanvas-title {
    color: #151515;
    font-family: "Poppins", sans-serif;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .offcanvas, .offcanvas-body {
    width: 600px !important;
    z-index: 1051;
  }

  .fixed-footer {
    z-index: 1052;
  }

  .fixed-footer.show {
    transform: translateY(0);
    display: flex !important;
  }

  #show-selected-images img {
    width: 100px;
    height: 100px;
    margin: 5px;
  }

  .emoji {
    font-size: 25px;
    padding: 5px;
  }

  // Legacy card styles - keeping for other components that might use them
  .card {
    border-radius: 20px;
    --bs-border-radius: 20px;
    background: white;
    padding: 30px;
    font-family: "Poppins", sans-serif;
    color: black;
    border: 0;
    text-decoration: none;
  }

  // Modern announcement card styles
  .reaction {
    font-size: 16px;
    margin-right: 8px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;

    small {
      font-size: 12px;
    }
  }

  .reaction-button {
    border: none;
    background: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.1);
    }
  }

  .reaction-popup {
    position: absolute;
    background: white;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    gap: 4px;

    .emoji {
      border: none;
      background: none;
      font-size: 18px;
      padding: 4px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }

  // Dark mode support
  @media (prefers-color-scheme: dark) {
    .reaction-popup {
      background: #2c2c2e;
      border-color: #3c3c3e;

      .emoji:hover {
        background-color: #3c3c3e;
      }
    }
  }

  // Modern modal styles
  .modal {
    &.show {
      display: flex !important;
      align-items: center;
      justify-content: center;
    }
  }

  // Quill editor styling within modal
  .ql-toolbar {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    border-color: #e5e5e5;
  }

  .ql-container {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    border-color: #e5e5e5;
  }

  // File upload styling
  #show-selected-images img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
  }

  // Simple filter dropdown styling
  #announcements-filter-dropdown {
    .filter-option {
      transition: background-color 0.15s ease;

      &:hover {
        background-color: #f9fafb;
      }
    }
  }

  .search-bar{
    height: 34.8px;
    width: 240px;
    border-radius: 20px;
    background: #FFF;
    box-shadow: 0 3px 14px 0 rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
  }
}

.select2-container {
  z-index: 1060;
}

.select2-selection__choice, .select2-selection__choice__remove{
  color: #212529 !important;
}

.select2-selection__choice__display{
  padding-left: 15px !important;
}
