<%
  # Default values
  title ||= "Progress"
  icon ||= "circle"
  total_value ||= 100
  unit ||= "%"
  segments ||= []
  
  # Calculate total for percentage calculation
  segments_total = segments.sum { |s| s[:value] }
  
  # Calculate angles for each segment (360 degrees total)
  current_angle = 0
  segment_data = segments.map do |segment|
    percentage = segments_total > 0 ? (segment[:value].to_f / segments_total * 100) : 0
    angle = percentage * 3.6 # Convert percentage to degrees
    
    data = {
      name: segment[:name],
      value: segment[:value],
      color: segment[:color],
      percentage: percentage,
      start_angle: current_angle,
      end_angle: current_angle + angle
    }
    
    current_angle += angle
    data
  end
%>

<div class="rounded-lg text-card-foreground shadow-sm flex flex-col bg-white dark:bg-slate-950 border border-slate-200 dark:border-slate-800">
  <!-- Header -->
  <div class="flex p-6 flex-row items-center gap-4 space-y-0 pb-2">
    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-300">
      <%= icon.html_safe %>
    </div>
    <div class="tracking-tight flex items-center justify-between text-lg font-semibold"><%= title %></div>
  </div>
  
  <!-- Chart Section -->
  <div class="p-6 flex-1 flex items-center justify-center relative py-4">
    <div class="mx-auto aspect-square h-[160px] w-[160px] relative">
      <!-- Simple CSS-based circular progress -->
      <div class="w-full h-full rounded-full relative overflow-hidden" style="background: conic-gradient(<%= segment_data.map { |s| "#{s[:color]} #{s[:start_angle]}deg #{s[:end_angle]}deg" }.join(', ') %>, #e5e7eb 0deg);">
        <!-- Inner circle to create donut effect -->
        <div class="absolute inset-4 bg-white dark:bg-slate-950 rounded-full"></div>
      </div>
      
      <!-- Center text -->
      <div class="absolute inset-0 flex flex-col items-center justify-center" aria-hidden="true">
        <span class="text-3xl font-bold tracking-tighter"><%= total_value %></span>
        <span class="text-sm text-muted-foreground"><%= unit %></span>
      </div>
    </div>
  </div>
  
  <!-- Legend -->
  <div class="flex flex-col gap-1 text-sm p-4 pt-0">
    <% segments.each do |segment| %>
      <div class="flex items-center justify-between p-2 rounded-md hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors">
        <div class="flex items-center gap-2">
          <span class="h-2.5 w-2.5 shrink-0 rounded-full" aria-hidden="true" style="background-color: <%= segment[:color] %>;"></span>
          <span class="text-muted-foreground"><%= segment[:name] %></span>
        </div>
        <span class="font-semibold text-right"><%= segment[:value] %></span>
      </div>
    <% end %>
  </div>
</div>
