<div class="w-full">
  <div class="min-h-screen bg-[#f5f5f7] dark:bg-[#1c1c1e] p-6 md:p-8">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Left side - Tables -->
      <div class="md:col-span-2 space-y-8">
        <!-- Upload Requests Table -->
        <div class="bg-white dark:bg-[#2c2c2e] rounded-xl shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md">
          <div class="bg-[#f8f8fa] dark:bg-[#323234] text-[#1d1d1f] dark:text-white p-4 border-b border-[#e5e5e7] dark:border-[#3a3a3c]">
            <h2 class="font-medium text-base">Upload Requests</h2>
          </div>
          <div class="overflow-hidden">
            <table class="w-full">
              <thead>
                <tr class="border-b border-[#e5e5e7] dark:border-[#3a3a3c]">
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white w-16"></th>
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white">Team Member</th>
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white">Form Name</th>
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white">Form Type</th>
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white">Category</th>
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white">Status</th>
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white"></th>
                </tr>
              </thead>
              <tbody>
                <% @incomplete_form_assignments.each do |form_assignment| %>
                  <tr class="border-b border-[#e5e5e7] dark:border-[#3a3a3c] last:border-0 hover:bg-[#f8f8fa] dark:hover:bg-[#323234] transition-colors">
                    <td class="p-4">
                      <div class="w-10 h-10 rounded-lg overflow-hidden bg-[#f5f5f7] dark:bg-[#323234] flex items-center justify-center">
                        <% if form_assignment.user&.image&.attached? %>
                          <%= image_tag form_assignment.user.image, alt: "User Avatar", class: "w-8 h-8 object-cover rounded" %>
                        <% else %>
                          <div class="w-8 h-8 bg-[#a8d8f0] rounded flex items-center justify-center text-white text-sm font-medium">
                            <%= form_assignment.user&.full_name&.first&.upcase || 'U' %>
                          </div>
                        <% end %>
                      </div>
                    </td>
                    <td class="p-4 text-sm text-[#1d1d1f] dark:text-[#e5e5e7]"><%= form_assignment.user&.full_name %></td>
                    <td class="p-4 text-sm text-[#1d1d1f] dark:text-[#e5e5e7]"><%= form_assignment.manual_title&.humanize || form_assignment.onboarding_form&.title %></td>
                    <td class="p-4 text-sm text-[#1d1d1f] dark:text-[#e5e5e7]"><%= form_assignment.onboarding_form&.form_type&.humanize || 'Manual' %></td>
                    <td class="p-4 text-sm text-[#1d1d1f] dark:text-[#e5e5e7]"><%= form_assignment.manual_document_category&.humanize || form_assignment.onboarding_form&.document_category&.humanize %></td>
                    <td class="p-4">
                      <div class="border focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent flex items-center gap-1 backdrop-blur-[1px] transition-all duration-300 hover:shadow-md bg-[#f2f2f7] hover:bg-[#e5e5ea] text-[#1d1d1f] border-none font-normal px-3 py-1 rounded-full text-xs">
                        Allocation Required
                      </div>
                    </td>
                    <td class="p-4">
                      <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-full h-8 w-8 text-[#86868b]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis-vertical h-4 w-4">
                          <circle cx="12" cy="12" r="1"></circle>
                          <circle cx="12" cy="5" r="1"></circle>
                          <circle cx="12" cy="19" r="1"></circle>
                        </svg>
                      </button>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Completed Uploads Table -->
        <div class="bg-white dark:bg-[#2c2c2e] rounded-xl shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md">
          <div class="bg-[#f8f8fa] dark:bg-[#323234] text-[#1d1d1f] dark:text-white p-4 border-b border-[#e5e5e7] dark:border-[#3a3a3c]">
            <h2 class="font-medium text-lg">Completed Uploads</h2>
          </div>
          <div class="overflow-hidden">
            <table class="w-full">
              <thead>
                <tr class="border-b border-[#e5e5e7] dark:border-[#3a3a3c]">
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white w-16"></th>
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white">Team Member</th>
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white">Form Name</th>
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white">Form Type</th>
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white">Category</th>
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white">Status</th>
                  <th class="text-left p-4 text-sm font-medium text-[#1d1d1f] dark:text-white"></th>
                </tr>
              </thead>
              <tbody>
                <% @completed_form_assignments.each do |form_assignment| %>
                  <tr class="border-b border-[#e5e5e7] dark:border-[#3a3a3c] last:border-0 hover:bg-[#f8f8fa] dark:hover:bg-[#323234] transition-colors">
                    <td class="p-4">
                      <div class="w-10 h-10 rounded-lg overflow-hidden bg-[#f5f5f7] dark:bg-[#323234] flex items-center justify-center">
                        <% if form_assignment.user&.image&.attached? %>
                          <%= image_tag form_assignment.user.image, alt: "User Avatar", class: "w-8 h-8 object-cover rounded" %>
                        <% else %>
                          <div class="w-8 h-8 bg-[#a8d8f0] rounded flex items-center justify-center text-white text-sm font-medium">
                            <%= form_assignment.user&.full_name&.first&.upcase || 'U' %>
                          </div>
                        <% end %>
                      </div>
                    </td>
                    <td class="p-4 text-sm text-[#1d1d1f] dark:text-[#e5e5e7]"><%= form_assignment.user&.full_name %></td>
                    <td class="p-4 text-sm text-[#1d1d1f] dark:text-[#e5e5e7]"><%= form_assignment.manual_title&.humanize || form_assignment.onboarding_form&.title %></td>
                    <td class="p-4 text-sm text-[#1d1d1f] dark:text-[#e5e5e7]"><%= form_assignment.onboarding_form&.form_type&.humanize || 'Manual' %></td>
                    <td class="p-4 text-sm text-[#1d1d1f] dark:text-[#e5e5e7]"><%= form_assignment.manual_document_category&.humanize || form_assignment.onboarding_form&.document_category&.humanize %></td>
                    <td class="p-4">
                      <div class="border focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent flex items-center gap-1 backdrop-blur-[1px] transition-all duration-300 hover:shadow-md bg-[#e8f5e9] hover:bg-[#d7eadb] text-[#1d1d1f] border-none font-normal px-3 py-1 rounded-full text-xs">
                        Approved
                      </div>
                    </td>
                    <td class="p-4">
                      <% if form_assignment.onboarding_form_id.present? || form_assignment.document.attached? %>
                        <div class="dropdown">
                          <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground rounded-full h-8 w-8 text-[#86868b]" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis-vertical h-4 w-4">
                              <circle cx="12" cy="12" r="1"></circle>
                              <circle cx="12" cy="5" r="1"></circle>
                              <circle cx="12" cy="19" r="1"></circle>
                            </svg>
                          </button>
                          <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <li>
                              <%= link_to "/admin/hr_management/form_assignments/#{form_assignment.id}", class: 'dropdown-item' do %>
                                View
                              <% end %>
                            </li>
                          </ul>
                        </div>
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Right side - Upload Form -->
      <div class="bg-white dark:bg-[#2c2c2e] rounded-xl shadow-sm p-6 transition-all duration-300 hover:shadow-md">
        <h2 class="text-lg font-medium text-[#1d1d1f] dark:text-white mb-6">Request Forms & Documents</h2>

        <%= form_with model: FormAssignment.new, url: admin_hr_management_form_assignments_path, method: 'post', local: true, class: 'space-y-5' do |f| %>
          <div>
            <label class="block text-sm font-medium text-[#6e6e73] dark:text-[#86868b] mb-2">Form(s) or Document(s)</label>
            <%= select_tag :onboarding_form_ids,
              options_for_select(@onboarding_forms.map { |form| [form.title, form.id] }),
              include_blank: 'Select Form or Document',
              class: 'flex border bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm w-full border-[#e5e5e7] dark:border-[#3a3a3c] focus-visible:ring-[#a8d8f0] rounded-lg h-11',
              id: 'documents-dropdown',
              multiple: false %>
          </div>

          <%= hidden_field_tag "selected_documents[]", "", multiple: true, id: "selected-documents-field" %>
          <div id="selected-documents-container"></div>

          <div>
            <label class="block text-sm font-medium text-[#6e6e73] dark:text-[#86868b] mb-2">Team Members</label>
            <%= select_tag :user_ids,
              options_for_select(@users.map { |u| [u.full_name, u.id] }),
              include_blank: 'Select Team Members',
              class: 'flex border bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm w-full border-[#e5e5e7] dark:border-[#3a3a3c] focus-visible:ring-[#a8d8f0] rounded-lg h-11',
              id: 'team-dropdown',
              multiple: false %>
          </div>

          <%= hidden_field_tag "selected_team_members[]", "", multiple: true, id: "selected-team-field" %>
          <div id="selected-team-container"></div>

          <div class="pt-4">
            <%= f.submit 'Submit Request', class: 'inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 px-4 py-2 w-full bg-[#a8d8f0] hover:bg-[#95c9e8] text-white rounded-full h-11 transition-all duration-200' %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
