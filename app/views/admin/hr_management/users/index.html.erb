<div id="employee-user-index-container">
  <%= render 'admin/hr_management/header' %>

  <div class="max-w-[1400px] mx-auto p-8">
    <div class="bg-gradient-to-b from-white to-[#f8f5fa] dark:from-[#2c2c3e] dark:to-[#262638] shadow-sm border border-[#e0ebfa] dark:border-[#3a4a5a] rounded-xl overflow-hidden">
      <!-- Header Section -->
      <div class="p-6 border-b border-[#e0ebfa] dark:border-[#3a4a5a]">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h2 class="text-xl font-semibold text-[#1d1d1f] dark:text-white tracking-tight">My Employees</h2>
            <p class="text-sm text-[#6e6e73] dark:text-[#a1a1a6] mt-1">Manage your team members and their information</p>
          </div>
          <button class="px-4 py-2 bg-[#0071e3] hover:bg-[#0077ed] text-white rounded-full text-sm font-medium transition-all duration-200 shadow-sm hover:shadow">
            Add Employee
          </button>
        </div>

        <!-- Search and Filter Section -->
        <div class="flex items-center gap-4">
          <div class="relative flex-1 max-w-md">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#8e8e93]">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.3-4.3"></path>
            </svg>
            <input placeholder="Search employees..." class="w-full pl-10 pr-4 py-2 bg-[#f5f5f7] dark:bg-[#3a3a3c] border border-[#e5e5e5] dark:border-[#4c4c4e] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#0071e3] dark:focus:ring-[#0071e3]/70" type="text">
          </div>

          <%= form_tag admin_hr_management_users_path, method: :get, class: 'contents' do %>
            <%= select_tag :role_id,
                           options_for_select([['All Departments', nil]] + @roles.map{ |role| [role.name.capitalize, role.id] }, selected: params[:role_id]),
                           class: 'px-3 py-2 bg-[#f5f5f7] dark:bg-[#3a3a3c] border border-[#e5e5e5] dark:border-[#4c4c4e] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#0071e3]',
                           onchange: 'this.form.submit()' %>
          <% end %>
        </div>
      </div>

      <!-- Table Section -->
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-[#f8f9fa] dark:bg-[#2a2a2c] border-b border-[#e0ebfa] dark:border-[#3a4a5a]">
            <tr>
              <th class="text-left py-3 px-6 text-xs font-medium text-[#6e6e73] dark:text-[#a1a1a6] uppercase tracking-wider">Employee</th>
              <th class="text-left py-3 px-6 text-xs font-medium text-[#6e6e73] dark:text-[#a1a1a6] uppercase tracking-wider">Role</th>
              <th class="text-left py-3 px-6 text-xs font-medium text-[#6e6e73] dark:text-[#a1a1a6] uppercase tracking-wider">Department</th>
              <th class="text-left py-3 px-6 text-xs font-medium text-[#6e6e73] dark:text-[#a1a1a6] uppercase tracking-wider">Status</th>
              <th class="text-left py-3 px-6 text-xs font-medium text-[#6e6e73] dark:text-[#a1a1a6] uppercase tracking-wider">Contact</th>
              <th class="text-right py-3 px-6 text-xs font-medium text-[#6e6e73] dark:text-[#a1a1a6] uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#e0ebfa] dark:divide-[#3a4a5a]">
            <% @users.each_with_index do |user, index| %>
              <tr class="hover:bg-[#f8f9fa] dark:hover:bg-[#2a2a2c] transition-colors duration-150">
                <!-- Employee Column -->
                <td class="py-4 px-6">
                  <div class="flex items-center gap-3">
                    <div class="h-10 w-10 rounded-full overflow-hidden bg-[#f5f5f7] dark:bg-[#3a3a3c] flex-shrink-0">
                      <% if user.image.attached? %>
                        <%= image_tag user.image, alt: user.full_name, loading: "lazy", width: "40", height: "40", class: "object-cover w-full h-full" %>
                      <% else %>
                        <div class="w-full h-full bg-[#0071e3] flex items-center justify-center text-white text-sm font-medium">
                          <%= user.initials %>
                        </div>
                      <% end %>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-[#1d1d1f] dark:text-white">
                        <%= user.full_name.present? ? user.full_name : "No Name Provided" %>
                      </div>
                      <div class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">
                        Employee ID: EMP<%= sprintf('%03d', user.id) %>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- Role Column -->
                <td class="py-4 px-6">
                  <div class="text-sm text-[#1d1d1f] dark:text-white">
                    <%= user.roles.pluck(:name).map(&:capitalize).join(', ').presence || 'No Role Assigned' %>
                  </div>
                </td>

                <!-- Department Column -->
                <td class="py-4 px-6">
                  <div class="text-sm text-[#1d1d1f] dark:text-white">
                    <% primary_role = user.roles.first&.name&.capitalize %>
                    <% case primary_role %>
                    <% when 'Dentist', 'Dental Hygienist', 'Dental Nurse' %>
                      Dental
                    <% when 'Receptionist', 'Reception' %>
                      Reception
                    <% when 'Practice Manager', 'Admin' %>
                      Administration
                    <% else %>
                      General
                    <% end %>
                  </div>
                </td>

                <!-- Status Column -->
                <td class="py-4 px-6">
                  <% if user.archived? %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#fee2e2] text-[#991b1b] dark:bg-[#991b1b]/20 dark:text-[#fca5a5]">
                      Archived
                    </span>
                  <% elsif index == 1 %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#fef3c7] text-[#92400e] dark:bg-[#92400e]/20 dark:text-[#fbbf24]">
                      On Leave
                    </span>
                  <% else %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#d1fae5] text-[#065f46] dark:bg-[#065f46]/20 dark:text-[#34d399]">
                      Active
                    </span>
                  <% end %>
                </td>

                <!-- Contact Column -->
                <td class="py-4 px-6">
                  <div class="text-sm text-[#1d1d1f] dark:text-white"><%= user.email %></div>
                  <% if user.mobile_phone.present? %>
                    <div class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]"><%= user.mobile_phone %></div>
                  <% end %>
                </td>

                <!-- Actions Column -->
                <td class="py-4 px-6 text-right">
                  <div class="flex items-center justify-end gap-2">
                    <%= link_to admin_hr_management_user_path(user) do %>
                      <button class="px-3 py-1.5 text-xs font-medium text-[#0071e3] bg-white dark:bg-[#3a3a3c] border border-[#e5e5e5] dark:border-[#4c4c4e] rounded-md hover:bg-[#f5f5f7] dark:hover:bg-[#4a4a4c] transition-colors">
                        View
                      </button>
                    <% end %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>

      <!-- Footer Section -->
      <div class="p-4 border-t border-[#e0ebfa] dark:border-[#3a4a5a] bg-[#f8f9fa] dark:bg-[#2a2a2c]">
        <div class="flex items-center justify-between text-sm text-[#6e6e73] dark:text-[#a1a1a6]">
          <span>Showing <%= @users.count %> of <%= @users.count %> employees</span>
          <div class="flex items-center gap-2">
            <button class="px-3 py-1 border border-[#e5e5e5] dark:border-[#4c4c4e] rounded-md hover:bg-white dark:hover:bg-[#3a3a3c] transition-colors">
              Previous
            </button>
            <button class="px-3 py-1 border border-[#e5e5e5] dark:border-[#4c4c4e] rounded-md hover:bg-white dark:hover:bg-[#3a3a3c] transition-colors">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

