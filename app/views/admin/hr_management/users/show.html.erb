<div id="employee-user-show-container" class="min-h-screen bg-[#f5f5f7] dark:bg-[#1c1c1e]">
  <%= render 'admin/hr_management/header' %>

  <div class="max-w-[1400px] mx-auto p-6">
    <!-- Back Button -->
    <div class="mb-6">
      <%= link_to admin_hr_management_users_path, class: "inline-flex items-center gap-2 text-[#6e6e73] dark:text-[#a1a1a6] hover:text-[#1d1d1f] dark:hover:text-white transition-colors" do %>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="m15 18-6-6 6-6"/>
        </svg>
        Back to My Employees
      <% end %>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">
      <!-- Left Column - Profile Card -->
      <div class="lg:col-span-1">
        <div class="bg-white dark:bg-[#2c2c2e] rounded-2xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-6">
          <!-- Practice Logo -->
          <div class="flex justify-center mb-4">
            <div class="w-16 h-16 bg-[#0071e3] rounded-2xl flex items-center justify-center">
              <span class="text-white font-bold text-xl">SM</span>
            </div>
          </div>

          <!-- User Info -->
          <div class="text-center mb-6">
            <h2 class="text-xl font-semibold text-[#1d1d1f] dark:text-white mb-1">
              <%= @user.full_name %>
            </h2>
            <p class="text-[#0071e3] text-sm font-medium mb-1">
              <%= @user.roles.first&.name&.titleize || 'Staff Member' %>
            </p>
            <p class="text-[#6e6e73] dark:text-[#a1a1a6] text-sm">
              <%= @user.roles.map(&:name).map(&:capitalize).join(', ').presence || 'General' %>
            </p>
          </div>

          <!-- Contact Info -->
          <div class="space-y-3 mb-6">
            <div class="flex items-center gap-3 text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73]">
                <rect width="20" height="16" x="2" y="4" rx="2"/>
                <path d="m22 7-10 5L2 7"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white"><%= @user.email %></span>
            </div>
            <% if @user.mobile_phone.present? %>
              <div class="flex items-center gap-3 text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73]">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
                <span class="text-[#1d1d1f] dark:text-white"><%= @user.mobile_phone %></span>
              </div>
            <% end %>
            <div class="flex items-center gap-3 text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73]">
                <path d="M8 2v4"/>
                <path d="M16 2v4"/>
                <rect width="18" height="18" x="3" y="4" rx="2"/>
                <path d="M3 10h18"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white">Joined on 15/08/2020</span>
            </div>
          </div>

          <!-- Send Message Button -->
          <button class="w-full bg-[#0071e3] hover:bg-[#0077ed] text-white rounded-xl py-3 px-4 font-medium transition-all duration-200 shadow-sm hover:shadow">
            Send Message
          </button>
        </div>
      </div>

      <!-- Right Column - Dashboard Cards -->
      <div class="lg:col-span-4">
        <!-- Stats Cards Row -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <!-- Leave Balance Card -->
          <div class="bg-white dark:bg-[#2c2c2e] rounded-2xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-6">
            <div class="flex items-center gap-3 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73]">
                <path d="M8 2v4"/>
                <path d="M16 2v4"/>
                <rect width="18" height="18" x="3" y="4" rx="2"/>
                <path d="M3 10h18"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white font-medium">Leave Balance</span>
            </div>

            <!-- Circular Progress -->
            <div class="relative w-24 h-24 mx-auto mb-4">
              <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                <!-- Background circle -->
                <circle cx="50" cy="50" r="40" stroke="#e5e5e7" stroke-width="8" fill="none"/>
                <!-- Progress circles -->
                <circle cx="50" cy="50" r="40" stroke="#ff6b6b" stroke-width="8" fill="none"
                        stroke-dasharray="75.4" stroke-dashoffset="67.86" stroke-linecap="round"/>
                <circle cx="50" cy="50" r="40" stroke="#ffd93d" stroke-width="8" fill="none"
                        stroke-dasharray="25.13" stroke-dashoffset="50.26" stroke-linecap="round"/>
                <circle cx="50" cy="50" r="40" stroke="#74c0fc" stroke-width="8" fill="none"
                        stroke-dasharray="175.93" stroke-dashoffset="25.13" stroke-linecap="round"/>
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="text-center">
                  <div class="text-2xl font-bold text-[#1d1d1f] dark:text-white">13</div>
                  <div class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">days</div>
                </div>
              </div>
            </div>

            <!-- Legend -->
            <div class="space-y-2 text-xs">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-[#ff6b6b]"></div>
                  <span class="text-[#6e6e73] dark:text-[#a1a1a6]">Sick</span>
                </div>
                <span class="text-[#1d1d1f] dark:text-white font-medium">3</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-[#ffd93d]"></div>
                  <span class="text-[#6e6e73] dark:text-[#a1a1a6]">Unpaid</span>
                </div>
                <span class="text-[#1d1d1f] dark:text-white font-medium">1</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-[#74c0fc]"></div>
                  <span class="text-[#6e6e73] dark:text-[#a1a1a6]">Remaining</span>
                </div>
                <span class="text-[#1d1d1f] dark:text-white font-medium">9</span>
              </div>
            </div>
          </div>

          <!-- Tasks Card -->
          <div class="bg-white dark:bg-[#2c2c2e] rounded-2xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-6">
            <div class="flex items-center gap-3 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73]">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white font-medium">Tasks</span>
            </div>

            <!-- Circular Progress -->
            <div class="relative w-24 h-24 mx-auto mb-4">
              <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                <!-- Background circle -->
                <circle cx="50" cy="50" r="40" stroke="#e5e5e7" stroke-width="8" fill="none"/>
                <!-- Progress circles -->
                <circle cx="50" cy="50" r="40" stroke="#34d399" stroke-width="8" fill="none"
                        stroke-dasharray="213.63" stroke-dashoffset="38.48" stroke-linecap="round"/>
                <circle cx="50" cy="50" r="40" stroke="#74c0fc" stroke-width="8" fill="none"
                        stroke-dasharray="25.13" stroke-dashoffset="213.63" stroke-linecap="round"/>
                <circle cx="50" cy="50" r="40" stroke="#ff6b6b" stroke-width="8" fill="none"
                        stroke-dasharray="12.57" stroke-dashoffset="238.76" stroke-linecap="round"/>
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="text-center">
                  <div class="text-2xl font-bold text-[#1d1d1f] dark:text-white">100</div>
                  <div class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">%</div>
                </div>
              </div>
            </div>

            <!-- Legend -->
            <div class="space-y-2 text-xs">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-[#34d399]"></div>
                  <span class="text-[#6e6e73] dark:text-[#a1a1a6]">Completed</span>
                </div>
                <span class="text-[#1d1d1f] dark:text-white font-medium">85</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-[#74c0fc]"></div>
                  <span class="text-[#6e6e73] dark:text-[#a1a1a6]">In Progress</span>
                </div>
                <span class="text-[#1d1d1f] dark:text-white font-medium">10</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-[#ff6b6b]"></div>
                  <span class="text-[#6e6e73] dark:text-[#a1a1a6]">Overdue</span>
                </div>
                <span class="text-[#1d1d1f] dark:text-white font-medium">5</span>
              </div>
            </div>
          </div>

          <!-- Attendance Card -->
          <div class="bg-white dark:bg-[#2c2c2e] rounded-2xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-6">
            <div class="flex items-center gap-3 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73]">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white font-medium">Attendance</span>
            </div>

            <!-- Circular Progress -->
            <div class="relative w-24 h-24 mx-auto mb-4">
              <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                <!-- Background circle -->
                <circle cx="50" cy="50" r="40" stroke="#e5e5e7" stroke-width="8" fill="none"/>
                <!-- Progress circles -->
                <circle cx="50" cy="50" r="40" stroke="#34d399" stroke-width="8" fill="none"
                        stroke-dasharray="238.76" stroke-dashoffset="12.57" stroke-linecap="round"/>
                <circle cx="50" cy="50" r="40" stroke="#ffd93d" stroke-width="8" fill="none"
                        stroke-dasharray="7.54" stroke-dashoffset="251.33" stroke-linecap="round"/>
                <circle cx="50" cy="50" r="40" stroke="#ff6b6b" stroke-width="8" fill="none"
                        stroke-dasharray="5.03" stroke-dashoffset="258.87" stroke-linecap="round"/>
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="text-center">
                  <div class="text-2xl font-bold text-[#1d1d1f] dark:text-white">100</div>
                  <div class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">%</div>
                </div>
              </div>
            </div>

            <!-- Legend -->
            <div class="space-y-2 text-xs">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-[#34d399]"></div>
                  <span class="text-[#6e6e73] dark:text-[#a1a1a6]">Present</span>
                </div>
                <span class="text-[#1d1d1f] dark:text-white font-medium">95</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-[#ffd93d]"></div>
                  <span class="text-[#6e6e73] dark:text-[#a1a1a6]">Late</span>
                </div>
                <span class="text-[#1d1d1f] dark:text-white font-medium">3</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-[#ff6b6b]"></div>
                  <span class="text-[#6e6e73] dark:text-[#a1a1a6]">Absent</span>
                </div>
                <span class="text-[#1d1d1f] dark:text-white font-medium">2</span>
              </div>
            </div>
          </div>

          <!-- Training Status Card -->
          <div class="bg-white dark:bg-[#2c2c2e] rounded-2xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-6">
            <div class="flex items-center gap-3 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73]">
                <path d="M22 10v6M2 10l10-5 10 5-10 5z"/>
                <path d="M6 12v5c3 3 9 3 12 0v-5"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white font-medium">Training Status</span>
            </div>

            <!-- Circular Progress -->
            <div class="relative w-24 h-24 mx-auto mb-4">
              <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                <!-- Background circle -->
                <circle cx="50" cy="50" r="40" stroke="#e5e5e7" stroke-width="8" fill="none"/>
                <!-- Progress circles -->
                <circle cx="50" cy="50" r="40" stroke="#34d399" stroke-width="8" fill="none"
                        stroke-dasharray="201.06" stroke-dashoffset="50.27" stroke-linecap="round"/>
                <circle cx="50" cy="50" r="40" stroke="#74c0fc" stroke-width="8" fill="none"
                        stroke-dasharray="37.7" stroke-dashoffset="251.33" stroke-linecap="round"/>
                <circle cx="50" cy="50" r="40" stroke="#ffd93d" stroke-width="8" fill="none"
                        stroke-dasharray="12.57" stroke-dashoffset="289.03" stroke-linecap="round"/>
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="text-center">
                  <div class="text-2xl font-bold text-[#1d1d1f] dark:text-white">100</div>
                  <div class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">%</div>
                </div>
              </div>
            </div>

            <!-- Legend -->
            <div class="space-y-2 text-xs">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-[#34d399]"></div>
                  <span class="text-[#6e6e73] dark:text-[#a1a1a6]">Completed</span>
                </div>
                <span class="text-[#1d1d1f] dark:text-white font-medium">80</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-[#74c0fc]"></div>
                  <span class="text-[#6e6e73] dark:text-[#a1a1a6]">In Progress</span>
                </div>
                <span class="text-[#1d1d1f] dark:text-white font-medium">15</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-[#ffd93d]"></div>
                  <span class="text-[#6e6e73] dark:text-[#a1a1a6]">Pending</span>
                </div>
                <span class="text-[#1d1d1f] dark:text-white font-medium">5</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons Row -->
        <div class="grid grid-cols-4 gap-4 mb-6">
          <button class="bg-white dark:bg-[#2c2c2e] rounded-xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-4 hover:shadow-md transition-all duration-200 group">
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73] group-hover:text-[#0071e3]">
                <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white font-medium">Timesheets</span>
            </div>
          </button>

          <button class="bg-white dark:bg-[#2c2c2e] rounded-xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-4 hover:shadow-md transition-all duration-200 group">
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73] group-hover:text-[#0071e3]">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white font-medium">Payslips</span>
            </div>
          </button>

          <button class="bg-white dark:bg-[#2c2c2e] rounded-xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-4 hover:shadow-md transition-all duration-200 group">
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73] group-hover:text-[#0071e3]">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white font-medium">Documents</span>
            </div>
          </button>

          <button class="bg-white dark:bg-[#2c2c2e] rounded-xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-4 hover:shadow-md transition-all duration-200 group">
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73] group-hover:text-[#0071e3]">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="m22 2-5 10-5-5 10-5z"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white font-medium">Permissions</span>
            </div>
          </button>
        </div>

        <!-- Calendar Section -->
        <div class="bg-white dark:bg-[#2c2c2e] rounded-2xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] overflow-hidden">
          <!-- Calendar Header -->
          <div class="p-6 border-b border-[#e5e5e7] dark:border-[#3a3a3c]">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4">
                <div class="relative">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#6e6e73]">
                    <circle cx="11" cy="11" r="8"/>
                    <path d="m21 21-4.3-4.3"/>
                  </svg>
                  <input type="text" placeholder="Search employees" class="pl-10 pr-4 py-2 bg-[#f5f5f7] dark:bg-[#3a3a3c] border border-[#e5e5e5] dark:border-[#4c4c4e] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#0071e3]">
                </div>
                <button class="px-4 py-2 bg-[#0071e3] text-white rounded-lg text-sm font-medium hover:bg-[#0077ed] transition-colors">
                  Today
                </button>
              </div>

              <div class="flex items-center gap-4">
                <div class="flex items-center gap-2">
                  <button class="p-2 hover:bg-[#f5f5f7] dark:hover:bg-[#3a3a3c] rounded-lg transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="m15 18-6-6 6-6"/>
                    </svg>
                  </button>
                  <span class="text-[#1d1d1f] dark:text-white font-medium">May 2025</span>
                  <button class="p-2 hover:bg-[#f5f5f7] dark:hover:bg-[#3a3a3c] rounded-lg transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="m9 18 6-6-6-6"/>
                    </svg>
                  </button>
                </div>

                <div class="flex bg-[#f5f5f7] dark:bg-[#3a3a3c] rounded-lg p-1">
                  <button class="px-3 py-1 text-sm text-[#6e6e73] dark:text-[#a1a1a6] hover:text-[#1d1d1f] dark:hover:text-white transition-colors">
                    Week
                  </button>
                  <button class="px-3 py-1 text-sm bg-[#0071e3] text-white rounded-md">
                    Month
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Calendar Content -->
          <div class="p-6">
            <!-- Calendar Grid would go here - simplified for now -->
            <div class="text-center py-12 text-[#6e6e73] dark:text-[#a1a1a6]">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-4 opacity-50">
                <path d="M8 2v4"/>
                <path d="M16 2v4"/>
                <rect width="18" height="18" x="3" y="4" rx="2"/>
                <path d="M3 10h18"/>
              </svg>
              <p>Calendar view will be implemented here</p>
              <p class="text-sm mt-2">This would show the employee's schedule, shifts, and time off</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>



