<!-- Modern Tailwind Modal -->
<div class="fixed inset-0 z-50 hidden overflow-y-auto" id="<%= id %>" aria-labelledby="announcementModalLabel" aria-hidden="true">
  <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
    <!-- Background overlay -->
    <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" aria-hidden="true"></div>

    <!-- Modal panel -->
    <div class="inline-block w-full max-w-2xl p-0 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-[#1c1c1e] shadow-xl rounded-2xl">
      <!-- Header -->
      <div class="flex items-center justify-between px-6 py-4 border-b border-[#e5e5e5] dark:border-[#3c3c3e]">
        <h3 class="text-lg font-semibold text-[#1d1d1f] dark:text-white" id="announcementModalLabel">
          <%= title %>
        </h3>
        <button type="button" class="p-2 text-[#6e6e73] hover:text-[#1d1d1f] dark:text-[#a1a1a6] dark:hover:text-white transition-colors rounded-full hover:bg-[#f5f5f7] dark:hover:bg-[#2c2c2e]" data-bs-dismiss="modal" aria-label="Close">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="m18 6-12 12"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Form Content -->
      <div class="px-6 py-6 max-h-[70vh] overflow-y-auto">
        <%= form_with(
              model: @announcement,
              url: url,
              local: true,
              html: { id: form_id, class: "space-y-6" }
            ) do |form| %>
          <%= form.hidden_field :id, id: "announcementId" %>

          <!-- Title Field -->
          <div class="space-y-2">
            <%= form.label :title, "Title", class: "block text-sm font-medium text-[#1d1d1f] dark:text-white" %>
            <%= form.text_field :title,
                  class: "w-full px-4 py-3 text-sm border border-[#e5e5e5] dark:border-[#3c3c3e] rounded-xl bg-white dark:bg-[#2c2c2e] text-[#1d1d1f] dark:text-white placeholder-[#8e8e93] focus:outline-none focus:ring-2 focus:ring-[#0071e3]/30 focus:border-[#0071e3] transition-colors",
                  placeholder: "Enter announcement title" %>
          </div>

          <!-- Text Field -->
          <div class="space-y-2">
            <%= form.label :text, "Content", class: "block text-sm font-medium text-[#1d1d1f] dark:text-white" %>
            <div class="border border-[#e5e5e5] dark:border-[#3c3c3e] rounded-xl overflow-hidden">
              <div id="_announcement_text" class="min-h-[200px]"></div>
            </div>
            <%= form.text_area :text, id: '_announcement_text_hidden', class: "hidden" %>
          </div>

          <!-- Topic Field -->
          <div class="space-y-2">
            <%= form.label :topic, "Category", class: "block text-sm font-medium text-[#1d1d1f] dark:text-white" %>
            <%= form.select :topic,
                  [["Breaking News", "breaking_news"], ["High Importance", "high_importance"],
                   ["Read Promptly", "read_promptly"], ["Read at leisure", "read_at_leisure"]],
                  { prompt: "Select a category" },
                  class: "w-full px-4 py-3 text-sm border border-[#e5e5e5] dark:border-[#3c3c3e] rounded-xl bg-white dark:bg-[#2c2c2e] text-[#1d1d1f] dark:text-white focus:outline-none focus:ring-2 focus:ring-[#0071e3]/30 focus:border-[#0071e3] transition-colors" %>
          </div>

          <!-- File Upload Field -->
          <div class="space-y-2">
            <%= form.label :files, "Attachments", class: "block text-sm font-medium text-[#1d1d1f] dark:text-white" %>
            <div class="border-2 border-dashed border-[#e5e5e5] dark:border-[#3c3c3e] rounded-xl p-8 text-center hover:border-[#0071e3] transition-colors" id="drop-zone">
              <div class="space-y-4">
                <div class="flex justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="text-[#8e8e93]">
                    <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                    <polyline points="14,2 14,8 20,8"></polyline>
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-[#1d1d1f] dark:text-white font-medium">Drag and drop files here</p>
                  <p class="text-xs text-[#8e8e93] mt-1">or</p>
                  <label id="fileInputLabel1" class="inline-flex items-center px-4 py-2 mt-2 text-sm font-medium text-[#0071e3] bg-[#f0f8ff] hover:bg-[#e6f3ff] rounded-lg cursor-pointer transition-colors">
                    Browse files
                  </label>
                </div>
                <div id="show-selected-images" class="grid grid-cols-3 gap-2 mt-4"></div>
              </div>
              <div class="hidden">
                <%= form.file_field :files, multiple: true, id: "fileInput" %>
              </div>
            </div>
          </div>

          <%= form.hidden_field :audience_teams %>
          <%= form.hidden_field :audience_roles %>

          <% if id == "offcanvasEditAnnouncement" %>
            <div class="pt-4">
              <button type="button" class="w-full px-4 py-3 text-sm font-medium text-white bg-[#0071e3] hover:bg-[#005bb5] rounded-xl transition-colors" onclick="submitEditForm()">
                Save Announcement
              </button>
            </div>
          <% end %>
        <% end %>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-end gap-3 px-6 py-4 border-t border-[#e5e5e5] dark:border-[#3c3c3e] bg-[#f5f5f7] dark:bg-[#2c2c2e]">
        <button type="button" class="px-4 py-2 text-sm font-medium text-[#6e6e73] hover:text-[#1d1d1f] dark:text-[#a1a1a6] dark:hover:text-white transition-colors" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="px-6 py-2 text-sm font-medium text-white bg-[#f5a76c] hover:bg-[#e0956b] rounded-lg transition-colors" data-bs-toggle="modal" data-bs-target="#audienceModal">
          Select Audience
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Quill Editor Styles -->
<link href="https://cdn.jsdelivr.net/npm/quill@2/dist/quill.snow.css" rel="stylesheet" data-turbo-track="reload"/>
<script data-turbo-track="reload" src="https://cdn.jsdelivr.net/npm/quill@2.0.2/dist/quill.js"></script>
