<%
  # Define color schemes for different announcement types
  color_schemes = {
    'breaking_news' => {
      bg: 'bg-[#fce7e7] dark:bg-[#3a2a2a]',
      border: 'bg-[#e05252]'
    },
    'high_importance' => {
      bg: 'bg-[#ffefd6] dark:bg-[#3a332a]',
      border: 'bg-[#e0a14a]'
    },
    'read_promptly' => {
      bg: 'bg-[#e7f0fc] dark:bg-[#2a2f3a]',
      border: 'bg-[#4a94e0]'
    },
    'read_at_leisure' => {
      bg: 'bg-[#fff8e0] dark:bg-[#3a382a]',
      border: 'bg-[#e0c04a]'
    },
    'info' => {
      bg: 'bg-[#e7fcef] dark:bg-[#2a3a30]',
      border: 'bg-[#4abe7c]'
    },
    'event' => {
      bg: 'bg-[#f0e7fc] dark:bg-[#302a3a]',
      border: 'bg-[#9a6dd7]'
    },
    'draft' => {
      bg: 'bg-[#f0f0f0] dark:bg-[#3a3a3a]',
      border: 'bg-[#8e8e93]'
    }
  }

  # Determine the color scheme based on announcement topic or status
  scheme_key = announcement.status == 'draft' ? 'draft' : (announcement.topic || 'info')
  scheme = color_schemes[scheme_key] || color_schemes['info']
%>

<div class="bg-white dark:bg-[#1c1c1e] rounded-2xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300" data-announcement-id="<%= announcement.id %>" id="hr_management_announcement" data-author="<%= announcement.author.full_name %>">
  <div class="relative px-6 pt-6 pb-5 <%= scheme[:bg] %>">
    <div class="absolute top-0 left-0 w-1 h-full <%= scheme[:border] %>"></div>
    <div class="flex items-start justify-between">
      <div class="flex items-start gap-4">
        <div class="h-10 w-10 rounded-full bg-white/70 dark:bg-[#2c2c2e] flex items-center justify-center shadow-sm">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-user-round h-6 w-6 text-[#6d7f9b] dark:text-[#a1a1a6]">
            <path d="M18 20a6 6 0 0 0-12 0"></path>
            <circle cx="12" cy="10" r="4"></circle>
            <circle cx="12" cy="12" r="10"></circle>
          </svg>
        </div>
        <div>
          <h2 class="text-base font-semibold text-[#1d1d1f] dark:text-white tracking-tight mb-1.5"><%= announcement.title %></h2>
          <div class="flex items-center gap-2">
            <span class="text-xs font-medium text-[#1d1d1f] dark:text-white"><%= announcement.author.full_name %></span>
            <span class="inline-block h-1 w-1 rounded-full bg-[#8e8e93] dark:bg-[#a1a1a6]"></span>
            <span class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]"><%= announcement.created_at.strftime("%d/%m/%Y, %H:%M") %></span>
          </div>
        </div>
      </div>
      <div class="flex items-center gap-2">
        <% if announcement.status == 'draft' %>
          <span class="inline-block px-3 py-1.5 rounded-full bg-white/60 dark:bg-[#2c2c2e]/60 text-sm font-medium text-[#1d1d1f] dark:text-white backdrop-blur-sm">Draft</span>
        <% else %>
          <span class="inline-block px-3 py-1.5 rounded-full bg-white/60 dark:bg-[#2c2c2e]/60 text-sm font-medium text-[#1d1d1f] dark:text-white backdrop-blur-sm"><%= announcement.topic.titleize %></span>
        <% end %>

        <% if current_user.has_permission?(:announcement, :update) %>
          <div class="dropdown">
            <button class="h-8 w-8 rounded-full flex items-center justify-center bg-white/60 dark:bg-[#2c2c2e]/60 backdrop-blur-sm dropdown-toggle" type="button" id="dropdownMenuButton-<%= announcement.id %>" data-bs-toggle="dropdown" aria-expanded="false">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis h-4 w-4 text-[#6e6e73] dark:text-[#a1a1a6]">
                <circle cx="12" cy="12" r="1"></circle>
                <circle cx="19" cy="12" r="1"></circle>
                <circle cx="5" cy="12" r="1"></circle>
              </svg>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton-<%= announcement.id %>">
              <% if announcement.status == 'draft' %>
                <li><a href="#" id="edit-announcement-<%= announcement.id %>" class="dropdown-item edit-announcement" data-announcement-id="<%= announcement.id %>">Edit</a></li>
                <li><%= link_to 'Publish', admin_hr_management_announcement_publish_path(announcement), method: :patch, class: 'dropdown-item' %></li>
              <% end %>
              <li><a href="#" class="dropdown-item edit-audience-announcement" data-announcement-id="<%= announcement.id %>">Edit audience</a></li>
              <li><a href="#" id="edit-announcement-<%= announcement.id %>" class="dropdown-item edit-announcement" data-announcement-id="<%= announcement.id %>">Edit</a></li>
              <li><%= link_to 'Delete', admin_hr_management_announcement_path(announcement), method: :delete, data: { confirm: 'Are you sure you want to delete this announcement?' }, class: 'dropdown-item text-danger' %></li>
            </ul>
          </div>
        <% end %>
      </div>
    </div>
  </div>
  <div class="px-6 py-5">
    <p class="text-sm leading-relaxed text-[#424245] dark:text-[#e5e5e5] mb-5"><%= sanitize announcement.text %></p>
    <div class="flex items-center justify-between pt-4 border-t border-[#f0f0f0] dark:border-[#2a2a2c]">
      <div class="flex items-center gap-2">
        <div class="reactions" data-announcement-id="<%= announcement.id %>">
          <span class="reaction-display text-xs text-[#6e6e73] dark:text-[#a1a1a6]">
            <% if announcement.reactions.empty? %>
              No reactions
            <% else %>
              <% announcement.reactions.each do |emoji, data| %>
                <span class="reaction" data-tippy-content="<%= data['users'].map { |u| u['name'] }.join(', ') + ' reacted' %>">
                  <%= emoji %> <small><%= data['count'] %></small>
                </span>
              <% end %>
            <% end %>
          </span>
        </div>
        <button class="flex items-center justify-center h-6 w-6 rounded-full bg-[#f5f5f7] dark:bg-[#2c2c2e] text-xs text-[#6e6e73] dark:text-[#a1a1a6] reaction-button">+</button>
        <div class="reaction-popup" style="display: none; position: absolute; background: white; border: 1px solid #ccc; padding: 5px; border-radius: 5px; z-index: 99">
          <button class="btn emoji" data-emoji="👍">👍</button>
          <button class="btn emoji" data-emoji="❤️">❤️</button>
          <button class="btn emoji" data-emoji="😂">😂</button>
          <button class="btn emoji" data-emoji="😢">😢</button>
          <button class="btn emoji" data-emoji="👏">👏</button>
        </div>
      </div>
      <div class="flex items-center">
        <span class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">
          <% if announcement.status == 'published' %>
            Shared with <%= pluralize(announcement.audience_roles.to_s.split(',').count + announcement.audience_teams.to_s.split(',').count, 'group') %>
          <% else %>
            Not shared yet
          <% end %>
        </span>
      </div>
    </div>
  </div>
  <div class="hidden">
    <div class="comments-popup mx-5" style="display: none;background: white; border-radius: 5px;">
      <h6 class="card-subtitle mgt-20 mgb-10">Comments</h6>
      <div class="comments-container"></div>
      <div class="load-more-comments" style="display: none;">
        <a href="#" class="load-more-link">Load more comments</a>
      </div>
      <form class="comment-form my-4" data-announcement-id="<%= announcement.id %>">
        <div class="input-group">
          <input type="text" class="form-control comment-input" placeholder="Write a comment...">
          <button class="btn btn-primary btn-sm comment-submit" type="submit">Post</button>
        </div>
      </form>
    </div>
  </div>
</div>
