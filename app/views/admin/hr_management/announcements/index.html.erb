<div id="announcements-container" class="w-full">
  <div class="min-h-screen bg-[#f5f5f7] dark:bg-[#1c1c1e] p-6">
    <div class="max-w-[1400px] mx-auto">
      <%= render 'admin/hr_management/header' %>

      <div class="flex items-center justify-end mb-8 pt-6">
        <div class="flex items-center gap-3">
          <div class="relative">
            <input
              placeholder="Search announcements..."
              class="w-64 h-10 pl-10 pr-4 rounded-full bg-white dark:bg-[#2c2c2e] border border-[#e5e5e5] dark:border-[#3c3c3e] text-sm focus:outline-none focus:ring-2 focus:ring-[#0071e3]/30"
              type="text"
              id="userAnnouncementSearch">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#8e8e93]">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.3-4.3"></path>
            </svg>
          </div>

          <div class="relative">
            <button
              id="announcements-filter-button"
              class="justify-center whitespace-nowrap ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-10 rounded-full bg-[#f5a76c] hover:bg-[#e0956b] text-white text-sm font-medium shadow-sm flex items-center gap-2 px-4">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-filter">
                <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"></polygon>
              </svg>
              <span id="filter-label">Filter: Most Recent</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down">
                <path d="m6 9 6 6 6-6"></path>
              </svg>
            </button>

            <div id="announcements-filter-dropdown" class="bg-white rounded-lg shadow-lg border border-gray-200 py-1 hidden absolute z-[1000] top-12 left-0 min-w-[200px]">
              <div class="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer filter-option active" data-sort="most_recent">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-green-600">
                  <path d="M20 6 9 17l-5-5"></path>
                </svg>
                <span class="text-sm text-gray-900">Most Recent</span>
              </div>

              <div class="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer filter-option" data-sort="breaking_news">
                <div class="w-4 h-4 mr-2"></div>
                <span class="text-sm text-gray-900">Breaking News</span>
              </div>

              <div class="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer filter-option" data-sort="high_importance">
                <div class="w-4 h-4 mr-2"></div>
                <span class="text-sm text-gray-900">High Importance</span>
              </div>

              <div class="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer filter-option" data-sort="read_promptly">
                <div class="w-4 h-4 mr-2"></div>
                <span class="text-sm text-gray-900">Read Promptly</span>
              </div>

              <div class="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer filter-option" data-sort="read_at_leisure">
                <div class="w-4 h-4 mr-2"></div>
                <span class="text-sm text-gray-900">Read at Leisure</span>
              </div>
            </div>
          </div>

          <% if current_user.has_permission?(:announcement, :create) %>
            <button
              id="create-announcement-btn"
              class="h-10 px-4 rounded-full bg-[#f8e3b0] text-[#1d1d1f] text-sm font-medium"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#createAnnouncementModal">
              Create Announcement
            </button>
          <% end %>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-6">
        <%= render partial: 'announcement', collection: @data[:drafts], locals: { draft: true } %>
        <%= render partial: 'announcement', collection: @data[:published] %>

        <% if @data[:published].empty? && @data[:drafts].empty? %>
          <div class="text-center mt-10 text-[#6e6e73] dark:text-[#a1a1a6]">
            No announcements
          </div>
        <% end %>
      </div>

      <%= render partial: 'form',
                 locals: {
                   title: "Create Announcement",
                   url: admin_hr_management_announcements_path,
                   id: 'createAnnouncementModal',
                   form_id: 'announcementForm'
                 } %>
    </div>
  </div>
</div>

  <div class="modal fade" id="audienceModal" tabindex="-1" aria-labelledby="audienceModalLabel" aria-hidden="true" style="z-index: 1053">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="audienceModalLabel">Select Audience</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <!-- Add checkboxes for teams and users -->
          <h6>Roles:</h6>
          <% @data[:roles].each do |role| %>
            <div class="form-check d-flex align-items-center">
              <input class="form-check-input" type="checkbox" value="<%= role.name.downcase %>" id="role_<%= role.name.downcase %>" name="audience_roles[]">
              <label class="form-check-label" for="role_<%= role.name.downcase %>">
                <%= role.name %>
              </label>
            </div>
          <% end %>

          <h6 class="mt-3">User:</h6>
          <div class="form-check" style="margin: 0 !important; padding: 0 !important; width: 250px;">
            <%= select_tag 'audience_teams[]', options_for_select(User.all.map { |user| [user.first_name, user.id] }), { class: 'form-control', style: 'width: 250px;', id: 'audience_teams', multiple: true } %>
          </div>

        </div>
        <div class="modal-footer d-flex align-items-center">
          <button type="button" class="cancelbtn" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="orangebtn align-self-center" id="publishAnnouncementFormButton">Publish</button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal fade" id="audienceEditModal" tabindex="-1" aria-labelledby="audienceEditModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="audienceModalLabel">Select Audience</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <!-- Add checkboxes for teams and users -->
          <%= hidden_field_tag :announcement_id %>
          <h6>Roles:</h6>
          <% @data[:roles].each do |role| %>
            <div class="form-check d-flex align-items-center">
              <input class="form-check-input" type="checkbox" value="<%= role.name.downcase %>" id="edit_role_<%= role.name.downcase %>" name="edit_audience_roles[]">
              <label class="form-check-label" for="role_<%= role.name.downcase %>">
                <%= role.name %>
              </label>
            </div>
          <% end %>

          <h6 class="mt-3">User:</h6>
          <div class="form-check" style="margin: 0 !important; padding: 0 !important; width: 250px;">
            <%= select_tag 'audience_teams[]', options_for_select(User.all.map { |user| [user.first_name, user.id] }), { class: 'form-control', id: 'edit_audience_teams', multiple: true } %>
          </div>

        </div>
        <div class="modal-footer d-flex align-items-center">
          <button type="button" class="cancelbtn" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="orangebtn align-self-center" id="updateAnnouncementAudienceFormButton">Update</button>
        </div>
      </div>
    </div>
  </div>
</div>
