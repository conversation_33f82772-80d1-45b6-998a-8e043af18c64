<!-- CONTACT DETAILS -->
<h2 class="text-lg font-semibold text-slate-800 mb-4">Contact details</h2>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
  <div>
    <%= f.label :email, class: "block text-sm font-medium text-slate-700 mb-1" %>
    <%= f.email_field :email, value: @user.email, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" %>
  </div>
  <div>
    <%= f.label :mobile_phone, "Mobile phone", class: "block text-sm font-medium text-slate-700 mb-1" %>
    <%= f.telephone_field :mobile_phone, value: @user.mobile_phone, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" %>
  </div>
</div>

<!-- MFA METHOD SECTION -->
<div class="mt-6">
  <h2 class="text-lg font-semibold text-slate-800 mb-4">Verification</h2>
  <div class="grid grid-cols-2 gap-6">
    <!-- Left half - Dynamic content based on selected method -->
    <div>
      <!-- SMS 2FA Content -->
      <div id="sms-content" class="mfa-content">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p class="text-sm text-blue-700">Each time verification is required, a code will be sent to your registered mobile number.</p>
        </div>
      </div>

      <!-- Pin Content -->
      <div id="pin-content" class="mfa-content hidden">
        <div class="space-y-4">
          <div>
            <%= f.label :pin, class: "block text-sm font-medium text-slate-700 mb-1" %>
            <%= f.password_field :pin, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2", placeholder: "Pin" %>
          </div>
          <div>
            <%= f.label :pin_confirmation, class: "block text-sm font-medium text-slate-700 mb-1" %>
            <%= f.password_field :pin_confirmation, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2", placeholder: "Pin confirmation" %>
          </div>
        </div>
      </div>

      <!-- Password Content -->
      <div id="password-content" class="mfa-content hidden">
        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
          <p class="text-sm text-orange-700">This will use your login password for verification. This is the same password you use to log into your account.</p>
        </div>
        <div class="space-y-4">
          <div>
            <%= f.label :password, class: "block text-sm font-medium text-slate-700 mb-1" %>
            <%= f.password_field :password, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2", placeholder: "Password" %>
          </div>
          <div>
            <%= f.label :password_confirmation, class: "block text-sm font-medium text-slate-700 mb-1" %>
            <%= f.password_field :password_confirmation, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2", placeholder: "Password confirmation" %>
          </div>
        </div>
      </div>
    </div>

    <!-- Right half - MFA method buttons -->
    <div class="flex gap-3 justify-start items-start">
      <button type="button" class="mfa-method-btn inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors bg-blue-100 text-blue-600 border border-blue-200 hover:bg-blue-200" data-method="sms">
        Use SMS 2FA
      </button>
      <button type="button" class="mfa-method-btn inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors bg-white text-gray-600 border border-gray-200 hover:bg-gray-50" data-method="pin">
        Use Pin
      </button>
      <button type="button" class="mfa-method-btn inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors bg-white text-gray-600 border border-gray-200 hover:bg-gray-50" data-method="password">
        Use Password
      </button>
    </div>
  </div>

  <%= f.hidden_field :mfa_method, value: @user.mfa_method || 'sms' %>
</div>
